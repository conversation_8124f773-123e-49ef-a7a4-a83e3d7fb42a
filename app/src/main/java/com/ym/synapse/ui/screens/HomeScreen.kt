package com.ym.synapse.ui.screens

import android.widget.Toast
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material.icons.filled.PhotoLibrary
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.runtime.rememberCoroutineScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.delay
import com.google.accompanist.swiperefresh.SwipeRefresh
import com.google.accompanist.swiperefresh.rememberSwipeRefreshState
import com.ym.synapse.service.NotionService
import com.ym.synapse.service.NotionResult
import com.ym.synapse.ui.animations.AnimatedListItem
import com.ym.synapse.ui.components.AnalysisHistoryCard

/**
 * 主页屏幕组件
 * 从MainActivity迁移而来，显示分析历史记录
 */
@Composable
fun HomeScreen() {
    val context = LocalContext.current
    val coroutineScope = rememberCoroutineScope()
    var historyList by remember { mutableStateOf(emptyList<com.ym.synapse.data.AnalysisRecord>()) }
    var selectedFilter by remember { mutableStateOf("全部") }
    var isRefreshing by remember { mutableStateOf(false) }

    // 刷新数据的函数
    val refreshData = suspend {
        isRefreshing = true
        try {
            // 模拟网络延迟，让用户看到刷新动画
            delay(500)
            historyList = com.ym.synapse.data.AnalysisHistoryManager.getHistoryList(context)
        } finally {
            isRefreshing = false
        }
    }

    // 初始加载和自动刷新
    LaunchedEffect(Unit) {
        refreshData()
    }

    // 筛选选项
    val filterOptions = listOf("全部", "Text-Heavy", "Rich-Content", "Simple-Image")

    // 根据筛选条件过滤列表
    val filteredList = if (selectedFilter == "全部") {
        historyList
    } else {
        historyList.filter { it.imageType == selectedFilter }
    }

    val swipeRefreshState = rememberSwipeRefreshState(isRefreshing)

    SwipeRefresh(
        state = swipeRefreshState,
        onRefresh = {
            coroutineScope.launch {
                refreshData()
            }
        }
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp)
        ) {
            // 标题和统计信息
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column {
                    Text(
                        text = "分析历史",
                        style = MaterialTheme.typography.headlineMedium,
                        fontWeight = FontWeight.Bold
                    )
                    Text(
                        text = "共 ${historyList.size} 条记录",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }

                // 操作按钮
                Row {
                    // 刷新按钮
                    IconButton(
                        onClick = {
                            coroutineScope.launch {
                                refreshData()
                            }
                            Toast.makeText(context, "正在刷新...", Toast.LENGTH_SHORT).show()
                        }
                    ) {
                        Icon(
                            imageVector = Icons.Default.Refresh,
                            contentDescription = "刷新",
                            tint = MaterialTheme.colorScheme.primary
                        )
                    }

                    // 清空历史按钮
                    if (historyList.isNotEmpty()) {
                        IconButton(
                            onClick = {
                                com.ym.synapse.data.AnalysisHistoryManager.clearHistory(context)
                                coroutineScope.launch {
                                    refreshData()
                                }
                                Toast.makeText(context, "历史记录已清空", Toast.LENGTH_SHORT).show()
                            }
                        ) {
                            Icon(
                                imageVector = Icons.Default.Delete,
                                contentDescription = "清空历史",
                                tint = MaterialTheme.colorScheme.error
                            )
                        }
                    }
                }
            }

            Spacer(modifier = Modifier.height(16.dp))

            // 筛选标签
            LazyRow(
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                items(filterOptions) { filter ->
                    FilterChip(
                        onClick = { selectedFilter = filter },
                        label = { Text(filter) },
                        selected = selectedFilter == filter
                    )
                }
            }

            Spacer(modifier = Modifier.height(16.dp))

            // 历史记录列表
            if (filteredList.isEmpty()) {
                // 空状态
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Icon(
                            imageVector = Icons.Default.PhotoLibrary,
                            contentDescription = null,
                            modifier = Modifier.size(64.dp),
                            tint = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                        Spacer(modifier = Modifier.height(16.dp))
                        Text(
                            text = if (selectedFilter == "全部") "暂无分析记录" else "暂无 $selectedFilter 类型记录",
                            style = MaterialTheme.typography.bodyLarge,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                        Spacer(modifier = Modifier.height(8.dp))
                        Text(
                            text = "双击电源键截图开始分析",
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                }
            } else {
                // 历史记录卡片列表
                LazyColumn(
                    verticalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    itemsIndexed(filteredList) { index, record ->
                        AnimatedListItem(
                            visible = true,
                            index = index
                        ) {
                            AnalysisHistoryCard(
                                record = record,
                                onCardClick = {
                                    // 点击卡片打开详细结果
                                    val intent = com.ym.synapse.ResultDisplayActivity.createIntent(
                                        context,
                                        record.imageType,
                                        record.resultText,
                                        record.imagePath,
                                        record.notionResult
                                    )
                                    context.startActivity(intent)
                                },
                                onDeleteClick = {
                                    com.ym.synapse.data.AnalysisHistoryManager.deleteRecord(context, record.id)
                                    coroutineScope.launch {
                                        refreshData()
                                    }
                                },
                                onSendToNotion = if (record.notionResult != null) {
                                    {
                                        coroutineScope.launch {
                                            val result = NotionService.getInstance().sendToNotion(
                                                context,
                                                record.notionResult,
                                                record.imageType,
                                                record.imagePath
                                            )

                                            val message = when (result) {
                                                is NotionResult.Success -> "已成功发送到Notion！"
                                                is NotionResult.Error -> "发送失败: ${result.message}"
                                            }

                                            Toast.makeText(context, message, Toast.LENGTH_LONG).show()
                                        }
                                    }
                                } else null
                            )
                        }
                    }
                }
            }
        }
    }
}
