package com.ym.synapse.ui.components

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material.icons.filled.Send
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import com.ym.synapse.ui.animations.bounceAnimation
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * 分析历史卡片组件
 * 从MainActivity迁移而来，用于显示分析记录
 */
@Composable
fun AnalysisHistoryCard(
    record: com.ym.synapse.data.AnalysisRecord,
    onCardClick: () -> Unit,
    onDeleteClick: () -> Unit,
    onSendToNotion: (() -> Unit)? = null
) {
    var isPressed by remember { mutableStateOf(false) }

    Card(
        modifier = Modifier
            .fillMaxWidth()
            .bounceAnimation(triggered = isPressed)
            .clickable {
                isPressed = true
                onCardClick()
                // 重置动画状态
                GlobalScope.launch {
                    delay(150)
                    isPressed = false
                }
            },
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // 标题行
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column(modifier = Modifier.weight(1f)) {
                    Text(
                        text = record.getAutoTitle(),
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Medium,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        // 类型标签
                        Surface(
                            color = when (record.imageType) {
                                "Text-Heavy" -> MaterialTheme.colorScheme.primaryContainer
                                "Rich-Content" -> MaterialTheme.colorScheme.secondaryContainer
                                "Simple-Image" -> MaterialTheme.colorScheme.tertiaryContainer
                                else -> MaterialTheme.colorScheme.surfaceVariant
                            },
                            shape = RoundedCornerShape(12.dp),
                            modifier = Modifier.padding(end = 8.dp)
                        ) {
                            Text(
                                text = when (record.imageType) {
                                    "Text-Heavy" -> "文字"
                                    "Rich-Content" -> "富内容"
                                    "Simple-Image" -> "图片"
                                    else -> record.imageType
                                },
                                style = MaterialTheme.typography.labelSmall,
                                modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp),
                                color = when (record.imageType) {
                                    "Text-Heavy" -> MaterialTheme.colorScheme.onPrimaryContainer
                                    "Rich-Content" -> MaterialTheme.colorScheme.onSecondaryContainer
                                    "Simple-Image" -> MaterialTheme.colorScheme.onTertiaryContainer
                                    else -> MaterialTheme.colorScheme.onSurfaceVariant
                                }
                            )
                        }

                        Text(
                            text = record.getFormattedTime(),
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                }

                Row {
                    // Notion按钮（如果有NotionAnalysisResult且提供了回调）
                    if (record.notionResult != null && onSendToNotion != null) {
                        IconButton(onClick = onSendToNotion) {
                            Icon(
                                imageVector = Icons.Default.Send,
                                contentDescription = "发送到Notion",
                                tint = MaterialTheme.colorScheme.primary
                            )
                        }
                    }

                    IconButton(onClick = onDeleteClick) {
                        Icon(
                            imageVector = Icons.Default.Delete,
                            contentDescription = "删除",
                            tint = MaterialTheme.colorScheme.error
                        )
                    }
                }
            }

            Spacer(modifier = Modifier.height(8.dp))

            // 预览文本
            Text(
                text = record.getPreviewText(),
                style = MaterialTheme.typography.bodyMedium,
                maxLines = 3,
                overflow = TextOverflow.Ellipsis,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}
