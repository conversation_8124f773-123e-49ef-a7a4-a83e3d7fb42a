# 标签管理功能增强

## 新增功能

### 1. 单个标签管理
✅ **编辑标签**：点击编辑按钮可以重命名标签
✅ **删除标签**：点击删除按钮可以删除单个标签
✅ **智能合并**：重命名时如果新标签已存在，会自动合并使用统计

### 2. 增强的用户体验
✅ **确认对话框**：删除和编辑操作都有确认对话框
✅ **Toast提示**：操作成功或失败都有明确的提示信息
✅ **异步处理**：使用协程处理操作，避免界面卡顿
✅ **实时刷新**：操作完成后自动刷新数据

### 3. 修复的问题
✅ **清理功能修复**：修复了标签清理无法使用的问题
✅ **错误处理**：添加了完善的错误处理机制
✅ **数据一致性**：确保标签数据的一致性和完整性

## 功能详情

### 标签编辑功能
- **重命名标签**：可以修改标签名称
- **智能合并**：如果新名称已存在，会合并使用统计
- **数据保持**：保留原有的使用次数、最后使用时间和图片类型关联

### 标签删除功能
- **单个删除**：可以删除任何单个标签
- **确认机制**：删除前需要确认，防止误操作
- **完全清理**：删除标签的所有相关数据

### 标签清理功能
- **低频清理**：清理使用次数少于2次的标签
- **批量操作**：一次性清理多个低频标签
- **统计更新**：清理后显示清理的标签数量

## 界面改进

### 标签项界面
```
[标签名称]           [高频/中频/低频] [编辑] [删除]
使用 X 次
```

### 操作按钮
- **编辑按钮**：蓝色编辑图标，点击弹出编辑对话框
- **删除按钮**：红色删除图标，点击弹出确认对话框
- **清理按钮**：顶部工具栏的清理按钮，批量清理低频标签

## 使用方法

### 编辑标签
1. 在标签管理界面找到要编辑的标签
2. 点击标签右侧的编辑图标
3. 在弹出的对话框中输入新的标签名称
4. 点击"保存"完成编辑

### 删除标签
1. 在标签管理界面找到要删除的标签
2. 点击标签右侧的删除图标
3. 在确认对话框中点击"删除"
4. 标签将被永久删除

### 清理低频标签
1. 在标签管理界面点击顶部的"清理"按钮
2. 在确认对话框中点击"确认清理"
3. 系统会自动删除使用次数少于2次的标签

## 技术实现

### 新增的TagManager方法
```kotlin
// 删除标签
fun deleteTag(tag: String)

// 重命名标签（支持智能合并）
fun renameTag(oldTag: String, newTag: String)
```

### 界面状态管理
- 使用`remember`管理对话框状态
- 使用`coroutineScope`处理异步操作
- 使用`Toast`提供用户反馈

### 错误处理
- 所有操作都包含try-catch错误处理
- 操作失败时显示具体错误信息
- 确保数据操作的原子性

## 注意事项

1. **数据备份**：删除操作不可撤销，建议重要标签谨慎删除
2. **标签合并**：重命名为已存在的标签时会自动合并数据
3. **清理阈值**：当前清理阈值设为2次，可根据需要调整
4. **性能考虑**：大量标签操作时可能需要一些时间

## 后续优化建议

1. **批量操作**：支持选择多个标签进行批量删除
2. **标签导入导出**：支持标签数据的备份和恢复
3. **标签分类**：支持为标签添加分类或颜色标记
4. **使用分析**：提供更详细的标签使用分析报告
